import 'dart:async';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:echipta/features/cart/domain/entities/order_entity.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:go_router/go_router.dart';

class WPaymentHandler extends StatefulWidget {
  const WPaymentHandler({
    super.key,
    required this.paymentUrl,
    required this.orderId,
    required this.paymentType,
  });

  final String paymentUrl;
  final int orderId;
  final PaymentType paymentType;

  @override
  State<WPaymentHandler> createState() => _WPaymentHandlerState();
}

class _WPaymentHandlerState extends State<WPaymentHandler> {
  @override
  void initState() {
    super.initState();
    _handlePayment();
  }

  void _handlePayment() async {
    // Handle different payment types
    if (widget.paymentType == PaymentType.alif && widget.paymentUrl.isNotEmpty) {
      await _handleAlifPayment();
    } else {
      // For balance payments or empty URLs, navigate directly to awaiting screen
      _navigateToPaymentAwaiting();
    }
  }

  Future<void> _handleAlifPayment() async {
    try {
      final Uri url = Uri.parse(widget.paymentUrl);
      print("🔍 Parsed URL: $url");

      // Try different launch modes for better compatibility
      bool launched = false;

      // First try: InAppWebView
      try {
        launched = await launchUrl(url, mode: LaunchMode.inAppWebView);
        print("✅ URL launched with InAppWebView: $launched");
      } catch (e) {
        print("❌ InAppWebView failed: $e");
      }

      // Second try: External browser if InAppWebView failed
      if (!launched) {
        try {
          launched = await launchUrl(url, mode: LaunchMode.externalApplication);
          print("✅ URL launched with external browser: $launched");
        } catch (e) {
          print("❌ External browser failed: $e");
        }
      }

      if (launched) {
        print("✅ URL launched successfully, will navigate to status after user returns");
        // Wait a bit for the webview to open, then navigate to status
        Future.delayed(Duration(seconds: 2), () {
          if (mounted) {
            _navigateToPaymentAwaiting();
          }
        });
      } else {
        print("❌ Failed to launch URL");
        _handlePaymentError();
      }
    } catch (e) {
      print("❌ Error handling Alif payment: $e");
      _handlePaymentError();
    }
  }

  void _navigateToPaymentAwaiting() {
    if (!mounted) return;

    // Navigate to payment awaiting screen (similar to order status)
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => WPaymentAwaitingScreen(
          orderId: widget.orderId,
          paymentType: widget.paymentType,
        ),
      ),
    );
  }

  void _handlePaymentError() {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('To\'lovda xatolik yuz berdi'),
        backgroundColor: AppColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        title: const Text(
          'To\'lov',
          style: TextStyle(
            color: AppColors.dark,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.close, color: AppColors.dark),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
            Gap(16),
            Text(
              'To\'lov ishga tushirilmoqda...',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Helper function to show payment handler
void showPaymentHandler(BuildContext context, String paymentUrl, int orderId, PaymentType paymentType) {
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => WPaymentHandler(
        paymentUrl: paymentUrl,
        orderId: orderId,
        paymentType: paymentType,
      ),
    ),
  );
}

// Payment Awaiting Screen - similar to order status screen
class WPaymentAwaitingScreen extends StatefulWidget {
  final int orderId;
  final PaymentType paymentType;

  const WPaymentAwaitingScreen({
    super.key,
    required this.orderId,
    required this.paymentType,
  });

  @override
  State<WPaymentAwaitingScreen> createState() => _WPaymentAwaitingScreenState();
}

class _WPaymentAwaitingScreenState extends State<WPaymentAwaitingScreen> {
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startStatusCheck();
  }

  void _startStatusCheck() {
    // Make initial call immediately
    context.read<CartBloc>().add(GetOrderStatus(orderId: widget.orderId));

    // Then start periodic polling
    _timer = Timer.periodic(Duration(seconds: 5), (timer) {
      context.read<CartBloc>().add(GetOrderStatus(orderId: widget.orderId));
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF9FAFB),
      body: BlocListener<CartBloc, CartState>(
        listener: (context, state) {
          // Handle payment completion
          if (state.currentOrderStatus?.isCompleted == true) {
            _timer?.cancel();
            _handlePaymentSuccess();
          }
        },
        child: BlocBuilder<CartBloc, CartState>(
          builder: (context, state) {
            final status = state.currentOrderStatus?.status ?? 0;
            return Padding(
              padding: const EdgeInsets.all(20.0),
              child: Center(
                child: Ink(
                  padding: const EdgeInsets.all(20.0),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        status == 1
                            ? AppAssets.wait
                            : status == 2
                            ? AppAssets.success
                            : status == 0
                            ? AppAssets.wait
                            : AppAssets.error,
                        width: MediaQuery.of(context).size.width * 0.5,
                      ),
                      Gap(18),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(18),
                          color: status == 1
                              ? AppColors.yellow3
                              : status == 2
                              ? AppColors.green3
                              : status == 0
                              ? AppColors.yellow3
                              : AppColors.red3,
                        ),
                        child: Text(
                          status == 1
                              ? "To'lov jarayonda"
                              : status == 2
                              ? "To'lov tasdiqlandi"
                              : status == 0
                              ? "To'lov tekshirilmoqda"
                              : "To'lov rad etildi",
                          style: context.textTheme.bodyLarge!.copyWith(
                            color: status == 1
                                ? AppColors.yellow
                                : status == 2
                                ? AppColors.green
                                : status == 0
                                ? AppColors.yellow
                                : AppColors.red,
                          ),
                        ),
                      ),
                      Gap(20),
                      Text(
                        _getStatusMessage(status),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.darkGrey,
                        ),
                      ),
                      Gap(30),
                      WButton(
                        onTap: () {
                          switch (status) {
                            case 0:
                              context.showCustomSnackbar(
                                "Kuting, to'lov holatini tekshirib boramiz",
                              );
                            case 1:
                              context.showCustomSnackbar(
                                "Kuting, to'lov holatini tekshirib boramiz",
                              );
                            case 2:
                              _handlePaymentSuccess();
                            default:
                              Navigator.pop(context);
                          }
                        },
                        txt: status == 1
                            ? "Tekshirish"
                            : status == 2
                            ? "Davom etish"
                            : status == 0
                            ? "Tekshirish"
                            : "Qayta urinish",
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  String _getStatusMessage(int status) {
    if (widget.paymentType == PaymentType.balance) {
      // Messages for balance payments
      switch (status) {
        case 1:
          return "Profil balansidan to'lov amalga oshirilmoqda. Iltimos kuting";
        case 2:
          return "Buyurtmangiz muvaffaqiyatli yaratildi va profil balansidan to'lov amalga oshirildi!";
        case 0:
          return "Profil balansi to'lovi tekshirilmoqda. Iltimos kuting";
        default:
          return "Profil balansida yetarli mablag' yo'q yoki xatolik yuz berdi";
      }
    } else {
      // Messages for Alif payments
      switch (status) {
        case 1:
          return "Alif Pay orqali to'lov amalga oshirish jarayonida. Iltimos kuting";
        case 2:
          return "Buyurtmangiz muvaffaqiyatli yaratildi va Alif Pay orqali to'lov tasdiqlandi!";
        case 0:
          return "Alif Pay to'lovi tekshirilmoqda. Iltimos kuting";
        default:
          return "Alif Pay to'lovida xatolik yuz berdi. Qaytadan harakat qilib ko'ring";
      }
    }
  }

  void _handlePaymentSuccess() {
    if (!mounted) return;

    // Clear cart and navigate back to main
    context.read<CartBloc>().add(const ClearCart());

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('To\'lov muvaffaqiyatli amalga oshirildi!'),
        backgroundColor: AppColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );

    // Navigate to main screen
    context.go(AppRouter.navigator);
  }
}
