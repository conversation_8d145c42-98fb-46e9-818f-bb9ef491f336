import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:echipta/features/cart/presentation/widgets/w_cart_item.dart';
import 'package:echipta/features/cart/presentation/widgets/w_delivery_options.dart';
import 'package:echipta/features/cart/presentation/widgets/w_cart_summary.dart';
import 'package:echipta/features/cart/presentation/widgets/w_cart_payment.dart';
import 'package:echipta/features/cart/presentation/widgets/w_payment_handler.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:formz/formz.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:go_router/go_router.dart';

import '../../domain/entities/order_entity.dart';

class WCartTab extends StatefulWidget {
  const WCartTab({super.key});

  @override
  State<WCartTab> createState() => _WCartTabState();
}

class _WCartTabState extends State<WCartTab> {
  int? _processedOrderId; // Track processed orders to prevent infinite loop

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, state) {
        if (state.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.shopping_cart_outlined,
                  size: 80,
                  color: AppColors.grey,
                ),
                Gap(16),
                Text(
                  'Savat bo\'sh',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.grey,
                  ),
                ),
                Gap(8),
                Text(
                  'Mahsulotlarni qo\'shish uchun\nbosh sahifaga o\'ting',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Cart Items
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.cartItems.length,
                      separatorBuilder: (context, index) => const Gap(12),
                      itemBuilder: (context, index) {
                        final item = state.cartItems[index];
                        return WCartItem(item: item);
                      },
                    ),
                    const Gap(24),
                    
                    // Delivery Options
                    const WDeliveryOptions(),
                    const Gap(24),

                    // Payment Options
                    const WCartPayment(),
                    const Gap(24),

                    // Cart Summary
                    const WCartSummary(),
                  ],
                ),
              ),
            ),
            
            // Bottom Action Button
            Container(
              margin: const EdgeInsets.all( 16),
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(12)),
                color: AppColors.primary,
                border: Border(
                  top: BorderSide(color: AppColors.lightGrey, width: 1),
                ),
              ),
              child: BlocListener<CartBloc, CartState>(
                listener: (context, state) {
                  if (state.orderStatus == FormzSubmissionStatus.success) {
                    // Add order to history
                    context.read<CartBloc>().add(
                      AddOrderToHistory(order: state.currentOrder!),
                    );

                    // Handle different payment types
                    if (state.paymentType == PaymentType.balance) {
                      // For balance payments, navigate directly to awaiting screen
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => WPaymentAwaitingScreen(
                            orderId: state.currentOrder!.orderId,
                            paymentType: state.paymentType,
                          ),
                        ),
                      );
                    } else if (state.currentOrder?.paymentUrl != null) {
                      // For Alif payments, use URL launcher directly (like order screen)
                      _handleAlifPayment(context, state.currentOrder!.paymentUrl!, state.currentOrder!.orderId);
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Buyurtma muvaffaqiyatli yaratildi!'),
                          backgroundColor: AppColors.success,
                        ),
                      );
                      // Clear cart for pickup orders
                      context.read<CartBloc>().add(const ClearCart());
                    }
                  } else if (state.orderStatus == FormzSubmissionStatus.failure) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Buyurtma yaratishda xatolik yuz berdi'),
                        backgroundColor: AppColors.error,
                      ),
                    );
                  }
                },
                child: WButton(
                  onTap: state.canCreateOrder
                      ? () {
                          context.read<CartBloc>().add(const CreateOrder());
                        }
                      : () {},
                  isLoading: state.orderStatus == FormzSubmissionStatus.inProgress,
                  txt: 'Buyurtma berish',
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

// Handle Alif payment using URL launcher (same as order screen)
Future<void> _handleAlifPayment(BuildContext context, String paymentUrl, int orderId) async {
  try {
    final Uri url = Uri.parse(paymentUrl);
    print("🔍 Parsed URL: $url");

    // Try different launch modes for better compatibility (same as order screen)
    bool launched = false;

    // First try: InAppWebView
    try {
      launched = await launchUrl(url, mode: LaunchMode.inAppWebView);
      print("✅ URL launched with InAppWebView: $launched");
    } catch (e) {
      print("❌ InAppWebView failed: $e");
    }

    // Second try: External browser if InAppWebView failed
    if (!launched) {
      try {
        launched = await launchUrl(url, mode: LaunchMode.externalApplication);
        print("✅ URL launched with external browser: $launched");
      } catch (e) {
        print("❌ External browser failed: $e");
      }
    }

    // Third try: Platform default
    if (!launched) {
      try {
        launched = await launchUrl(url);
        print("✅ URL launched with platform default: $launched");
      } catch (e) {
        print("❌ Platform default failed: $e");
      }
    }

    if (launched) {
      print("✅ URL launched successfully, will navigate to status after user returns");
      // Wait a bit for the webview to open, then navigate to status
      Future.delayed(Duration(seconds: 2), () {
        if (context.mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => WPaymentAwaitingScreen(
                orderId: orderId,
                paymentType: PaymentType.alif,
              ),
            ),
          );
        }
      });
    } else {
      print("❌ All URL launch methods failed");
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("To'lov sahifasini ochishda xatolik yuz berdi"),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  } catch (e) {
    print("❌ URL parsing failed: $e");
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Noto'g'ri to'lov havolasi"),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
