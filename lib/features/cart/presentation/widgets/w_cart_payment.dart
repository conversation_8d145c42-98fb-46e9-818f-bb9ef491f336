import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:echipta/features/cart/domain/entities/order_entity.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:go_router/go_router.dart';

class WCartPayment extends StatelessWidget {
  const WCartPayment({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "To'lov turi",
          style: context.textTheme.displayMedium!.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const Gap(20),
        BlocBuilder<ProfileBloc, ProfileState>(
          builder: (context, profileState) {
            return ListTile(
              onTap: () {
                context.read<CartBloc>().add(
                  const SetPaymentType(
                    paymentType: PaymentType.balance,
                  ),
                );
              },
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(100),
              ),
              tileColor: AppColors.primary3,
              leading: const CircleAvatar(
                backgroundColor: AppColors.primary,
                child: Icon(Icons.person, color: AppColors.white),
              ),
              trailing: BlocBuilder<CartBloc, CartState>(
                builder: (context, state) {
                  return Icon(
                    state.paymentType == PaymentType.balance
                        ? Icons.check_circle
                        : Icons.circle_outlined,
                    color: AppColors.primary,
                    size: 42,
                  );
                },
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16),
              title: Text("Profil balansi"),
              subtitle: Text(
                "Balans: ${profileState.me.balance} so'm",
                style: TextStyle(
                  color: AppColors.grey,
                  fontSize: 12,
                ),
              ),
            );
          },
        ),
        const Gap(16),
        ListTile(
          onTap: () {
            context.read<CartBloc>().add(
              const SetPaymentType(
                paymentType: PaymentType.alif,
              ),
            );
          },
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100),
          ),
          tileColor: AppColors.primary3,
          leading: CircleAvatar(
            backgroundColor: AppColors.primary,
            child: SvgPicture.asset(
              AppAssets.alif,
              color: AppColors.white,
              width: 28,
            ),
          ),
          trailing: BlocBuilder<CartBloc, CartState>(
            builder: (context, state) {
              return Icon(
                state.paymentType == PaymentType.alif
                    ? Icons.check_circle
                    : Icons.circle_outlined,
                color: AppColors.primary,
                size: 42,
              );
            },
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16),
          title: Text(LocaleKeys.alifPay.tr()),
        ),
      ],
    );
  }
}
